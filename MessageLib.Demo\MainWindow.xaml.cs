﻿using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using MessageLib.Core.Models;

// Use aliases to avoid conflicts with System.Windows namespace
using ThemedMessageBox = MessageLib.Core.MessageBox;
using ThemedMessageBoxResult = MessageLib.Core.Models.ThemedMessageBoxResult;
using ThemedMessageBoxOptions = MessageLib.Core.Models.ThemedMessageBoxOptions;
using ThemedMessageBoxButton = MessageLib.Core.Models.MessageBoxButton;

namespace MessageLib.Demo
{
    /// <summary>
    /// Interaction logic for MainWindow.xaml
    /// </summary>
    public partial class MainWindow : Window
    {
        public MainWindow()
        {
            InitializeComponent();
        }

        private void SimpleMessage_Click(object sender, RoutedEventArgs e)
        {
            var result = ThemedMessageBox.Show("This is a simple message box.", "Simple Message");
            ShowResult(result);
        }

        private void InformationMessage_Click(object sender, RoutedEventArgs e)
        {
            var result = ThemedMessageBox.ShowInformation("This is an information message.", "Information");
            ShowResult(result);
        }

        private void WarningMessage_Click(object sender, RoutedEventArgs e)
        {
            var result = ThemedMessageBox.ShowWarning("This is a warning message.", "Warning");
            ShowResult(result);
        }

        private void ErrorMessage_Click(object sender, RoutedEventArgs e)
        {
            var result = ThemedMessageBox.ShowError("This is an error message.", "Error");
            ShowResult(result);
        }

        private void SuccessMessage_Click(object sender, RoutedEventArgs e)
        {
            var result = ThemedMessageBox.ShowSuccess("This is a success message.", "Success");
            ShowResult(result);
        }

        private void QuestionMessage_Click(object sender, RoutedEventArgs e)
        {
            var result = ThemedMessageBox.ShowQuestion("This is a question message?", "Question");
            ShowResult(result);
        }

        private void ConfirmationMessage_Click(object sender, RoutedEventArgs e)
        {
            var result = ThemedMessageBox.ShowConfirmation("Do you want to proceed?", "Confirmation");
            ShowResult(result);
        }

        private void CustomButtons_Click(object sender, RoutedEventArgs e)
        {
            var options = new ThemedMessageBoxOptions
            {
                Title = "Custom Buttons",
                Message = "This message box has custom buttons.",
                MessageType = MessageBoxType.Question,
                Buttons = MessageBoxButtons.Custom,
                CustomButtons = new[]
                {
                    new ThemedMessageBoxButton { Text = "Proceed", Result = ThemedMessageBoxResult.Yes, IsDefault = true },
                    new ThemedMessageBoxButton { Text = "Skip", Result = ThemedMessageBoxResult.No },
                    new ThemedMessageBoxButton { Text = "Abort", Result = ThemedMessageBoxResult.Cancel, IsCancel = true }
                }
            };

            var result = ThemedMessageBox.Show(options);
            ShowResult(result);
        }

        private void RichContent_Click(object sender, RoutedEventArgs e)
        {
            var content = new StackPanel
            {
                Margin = new Thickness(8)
            };

            content.Children.Add(new TextBlock
            {
                Text = "This is a rich content message box.",
                FontWeight = FontWeights.Bold,
                Margin = new Thickness(0, 0, 0, 8)
            });

            content.Children.Add(new TextBlock
            {
                Text = "You can add any UIElement as content.",
                TextWrapping = TextWrapping.Wrap,
                Margin = new Thickness(0, 0, 0, 8)
            });

            var checkBox = new CheckBox
            {
                Content = "Don't show this again",
                Margin = new Thickness(0, 8, 0, 0)
            };
            content.Children.Add(checkBox);

            var options = new ThemedMessageBoxOptions
            {
                Title = "Rich Content",
                ContentElement = content,
                MessageType = MessageBoxType.Information,
                Buttons = MessageBoxButtons.OKCancel
            };

            var result = ThemedMessageBox.Show(options);
            ShowResult(result);

            if (result == ThemedMessageBoxResult.OK && checkBox.IsChecked == true)
            {
                ThemedMessageBox.ShowInformation("You checked 'Don't show this again'.");
            }
        }

        private async void AsyncMessage_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            if (button != null)
            {
                button.IsEnabled = false;
                button.Content = "Processing...";
            }

            // Simulate some async work
            await Task.Delay(2000);

            var result = await ThemedMessageBox.ShowQuestionAsync("This message box was shown asynchronously. Did you notice?");
            ShowResult(result);

            if (button != null)
            {
                button.IsEnabled = true;
                button.Content = "Async Message Box";
            }
        }

        private void LightTheme_Click(object sender, RoutedEventArgs e)
        {
            var options = new ThemedMessageBoxOptions
            {
                Title = "Light Theme",
                Message = "This message box uses the light theme. You should see a white background with dark text and blue buttons.",
                MessageType = MessageBoxType.Information,
                Buttons = MessageBoxButtons.OK,
                UseLightTheme = true,
                Width = 450,
                MinHeight = 250
            };

            ThemedMessageBox.Show(options);
        }

        private void DarkTheme_Click(object sender, RoutedEventArgs e)
        {
            var options = new ThemedMessageBoxOptions
            {
                Title = "Dark Theme",
                Message = "This message box uses the dark theme. You should see a dark background with light text and blue buttons.",
                MessageType = MessageBoxType.Information,
                Buttons = MessageBoxButtons.OK,
                UseLightTheme = false,
                Width = 450,
                MinHeight = 250
            };

            ThemedMessageBox.Show(options);
        }

        private void CustomColors_Click(object sender, RoutedEventArgs e)
        {
            var options = new ThemedMessageBoxOptions
            {
                Title = "Custom Colors",
                Message = "This message box uses custom colors. You should see purple buttons regardless of the theme.",
                MessageType = MessageBoxType.Information,
                Buttons = MessageBoxButtons.OK,
                PrimaryColor = Colors.Purple,
                AccentColor = Colors.Orange,
                Width = 450,
                MinHeight = 250
            };

            ThemedMessageBox.Show(options);
        }

        private void WithAnimation_Click(object sender, RoutedEventArgs e)
        {
            var options = new ThemedMessageBoxOptions
            {
                Title = "With Animation",
                Message = "This message box uses animations. You should see a fade-in and scale animation when the message box appears and a fade-out animation when it closes.",
                MessageType = MessageBoxType.Information,
                Buttons = MessageBoxButtons.OK,
                UseAnimations = true,
                Width = 500,
                MinHeight = 250
            };

            ThemedMessageBox.Show(options);
        }

        private void WithoutAnimation_Click(object sender, RoutedEventArgs e)
        {
            var options = new ThemedMessageBoxOptions
            {
                Title = "Without Animation",
                Message = "This message box does not use animations. It should appear and disappear instantly without any transition effects.",
                MessageType = MessageBoxType.Information,
                Buttons = MessageBoxButtons.OK,
                UseAnimations = false,
                Width = 500,
                MinHeight = 250
            };

            ThemedMessageBox.Show(options);
        }

        private void WithTitleBar_Click(object sender, RoutedEventArgs e)
        {
            var options = new ThemedMessageBoxOptions
            {
                Title = "With Title Bar",
                Message = "This message box has a title bar. You should see the standard window title bar with a close button.",
                MessageType = MessageBoxType.Information,
                Buttons = MessageBoxButtons.OK,
                ShowTitleBar = true,
                Width = 500,
                MinHeight = 250
            };

            ThemedMessageBox.Show(options);
        }

        private void WithoutTitleBar_Click(object sender, RoutedEventArgs e)
        {
            var options = new ThemedMessageBoxOptions
            {
                Title = "Without Title Bar",
                Message = "This message box does not have a title bar. You should see a borderless window without the standard window controls.",
                MessageType = MessageBoxType.Information,
                Buttons = MessageBoxButtons.OK,
                ShowTitleBar = false,
                Width = 500,
                MinHeight = 250
            };

            ThemedMessageBox.Show(options);
        }

        private void ShowResult(ThemedMessageBoxResult result)
        {
            ThemedMessageBox.ShowInformation($"Result: {result}", "Result");
        }
    }
}