using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace MessageLib.Core.Converters
{
    /// <summary>
    /// Converts a boolean value to a resource from a ResourceDictionary
    /// </summary>
    public class BooleanToResourceConverter : IValueConverter
    {
        /// <summary>
        /// Gets or sets the value to use when the boolean is true
        /// </summary>
        public ResourceDictionary TrueValue { get; set; }

        /// <summary>
        /// Gets or sets the value to use when the boolean is false
        /// </summary>
        public ResourceDictionary FalseValue { get; set; }

        /// <summary>
        /// Converts a boolean value to a resource
        /// </summary>
        /// <param name="value">The boolean value to convert</param>
        /// <param name="targetType">The type of the target property</param>
        /// <param name="parameter">The resource key to look up</param>
        /// <param name="culture">The culture to use for conversion</param>
        /// <returns>The resource from the appropriate ResourceDictionary</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool boolValue = value is bool b && b;
            string resourceKey = parameter as string;

            if (string.IsNullOrEmpty(resourceKey))
            {
                return null;
            }

            ResourceDictionary dictionary = boolValue ? TrueValue : FalseValue;
            
            if (dictionary == null)
            {
                return null;
            }

            if (dictionary.Contains(resourceKey))
            {
                var resource = dictionary[resourceKey];
                
                // If the target type is Color and the resource is a SolidColorBrush, extract the color
                if (targetType == typeof(Color) && resource is SolidColorBrush brush)
                {
                    return brush.Color;
                }
                
                return resource;
            }

            return null;
        }

        /// <summary>
        /// Converts a resource back to a boolean value (not implemented)
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
