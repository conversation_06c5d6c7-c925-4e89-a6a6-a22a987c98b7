using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using MessageLib.Core.Models;
using MessageLib.Core.ViewModels;
using MessageLib.Core.Views;

namespace MessageLib.Core.Services
{
    /// <summary>
    /// Implementation of the message box service
    /// </summary>
    public class MessageBoxService : IMessageBoxService
    {
        /// <summary>
        /// Shows a message box with the specified options
        /// </summary>
        /// <param name="options">The options for the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public ThemedMessageBoxResult Show(ThemedMessageBoxOptions options)
        {
            ThemedMessageBoxResult result = ThemedMessageBoxResult.Cancel;

            try
            {
                // Ensure we're on the UI thread
                if (Application.Current?.Dispatcher != null && !Application.Current.Dispatcher.CheckAccess())
                {
                    return Application.Current.Dispatcher.Invoke(() => Show(options));
                }

                // Create the view model and initialize it with the options
                var viewModel = new MessageBoxViewModel();
                viewModel.Initialize(options);

                // Create the message box window
                var messageBox = new MessageBoxView();

                // Set basic window properties
                messageBox.Title = options.Title ?? "Message";
                messageBox.WindowStartupLocation = WindowStartupLocation.CenterScreen;
                messageBox.ShowInTaskbar = false;
                messageBox.Topmost = true;

                // Set the window style before setting the DataContext
                try
                {
                    if (!options.ShowTitleBar)
                    {
                        messageBox.WindowStyle = WindowStyle.None;
                        messageBox.AllowsTransparency = true;
                        messageBox.BorderThickness = new Thickness(0);
                    }
                    else
                    {
                        messageBox.WindowStyle = WindowStyle.SingleBorderWindow;
                        messageBox.AllowsTransparency = false;
                        messageBox.BorderThickness = new Thickness(1);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Error setting window style: {ex.Message}");
                    // Fallback to default style
                    messageBox.WindowStyle = WindowStyle.SingleBorderWindow;
                    messageBox.AllowsTransparency = false;
                }

                // Set the DataContext after window style is configured
                messageBox.DataContext = viewModel;

                // Set the owner if specified
                if (options.Owner != null)
                {
                    try
                    {
                        messageBox.Owner = options.Owner;
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"Error setting owner: {ex.Message}");
                    }
                }

                // Show the dialog and wait for it to close
                var dialogResult = messageBox.ShowDialog();

                // Get the result from the view model
                result = viewModel.Result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error showing message box: {ex.Message}");
                result = ThemedMessageBoxResult.Cancel;
            }

            return result;
        }

        /// <summary>
        /// Shows a message box with the specified options asynchronously
        /// </summary>
        /// <param name="options">The options for the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public async Task<ThemedMessageBoxResult> ShowAsync(ThemedMessageBoxOptions options)
        {
            // For asynchronous calls, use the dispatcher to ensure we're on the UI thread
            if (Application.Current?.Dispatcher != null)
            {
                return await Application.Current.Dispatcher.InvokeAsync(() => Show(options));
            }
            else
            {
                // If we don't have a dispatcher, we need to create a task completion source
                var tcs = new TaskCompletionSource<ThemedMessageBoxResult>();

                // Create a new STA thread to show the dialog
                var thread = new Thread(() =>
                {
                    try
                    {
                        var result = Show(options);
                        tcs.SetResult(result);
                    }
                    catch (Exception ex)
                    {
                        tcs.SetException(ex);
                    }
                });

                // Set the thread as STA
                thread.SetApartmentState(ApartmentState.STA);
                thread.Start();

                // Return the task
                return await tcs.Task;
            }
        }

        /// <summary>
        /// Shows a simple message box with the specified message
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public ThemedMessageBoxResult Show(string message, string title = "Message")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Information,
                Buttons = MessageBoxButtons.OK
            };

            return Show(options);
        }

        /// <summary>
        /// Shows a simple message box with the specified message asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public Task<ThemedMessageBoxResult> ShowAsync(string message, string title = "Message")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Information,
                Buttons = MessageBoxButtons.OK
            };

            return ShowAsync(options);
        }

        /// <summary>
        /// Shows an information message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public ThemedMessageBoxResult ShowInformation(string message, string title = "Information")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Information,
                Buttons = MessageBoxButtons.OK
            };

            return Show(options);
        }

        /// <summary>
        /// Shows an information message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public Task<ThemedMessageBoxResult> ShowInformationAsync(string message, string title = "Information")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Information,
                Buttons = MessageBoxButtons.OK
            };

            return ShowAsync(options);
        }

        /// <summary>
        /// Shows a warning message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public ThemedMessageBoxResult ShowWarning(string message, string title = "Warning")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Warning,
                Buttons = MessageBoxButtons.OK
            };

            return Show(options);
        }

        /// <summary>
        /// Shows a warning message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public Task<ThemedMessageBoxResult> ShowWarningAsync(string message, string title = "Warning")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Warning,
                Buttons = MessageBoxButtons.OK
            };

            return ShowAsync(options);
        }

        /// <summary>
        /// Shows an error message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public ThemedMessageBoxResult ShowError(string message, string title = "Error")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Error,
                Buttons = MessageBoxButtons.OK
            };

            return Show(options);
        }

        /// <summary>
        /// Shows an error message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public Task<ThemedMessageBoxResult> ShowErrorAsync(string message, string title = "Error")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Error,
                Buttons = MessageBoxButtons.OK
            };

            return ShowAsync(options);
        }

        /// <summary>
        /// Shows a success message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public ThemedMessageBoxResult ShowSuccess(string message, string title = "Success")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Success,
                Buttons = MessageBoxButtons.OK
            };

            return Show(options);
        }

        /// <summary>
        /// Shows a success message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public Task<ThemedMessageBoxResult> ShowSuccessAsync(string message, string title = "Success")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Success,
                Buttons = MessageBoxButtons.OK
            };

            return ShowAsync(options);
        }

        /// <summary>
        /// Shows a question message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public ThemedMessageBoxResult ShowQuestion(string message, string title = "Question")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Question,
                Buttons = MessageBoxButtons.YesNo
            };

            return Show(options);
        }

        /// <summary>
        /// Shows a question message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public Task<ThemedMessageBoxResult> ShowQuestionAsync(string message, string title = "Question")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Question,
                Buttons = MessageBoxButtons.YesNo
            };

            return ShowAsync(options);
        }

        /// <summary>
        /// Shows a confirmation message box with Yes/No buttons
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public ThemedMessageBoxResult ShowConfirmation(string message, string title = "Confirmation")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Question,
                Buttons = MessageBoxButtons.YesNo
            };

            return Show(options);
        }

        /// <summary>
        /// Shows a confirmation message box with Yes/No buttons asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public Task<ThemedMessageBoxResult> ShowConfirmationAsync(string message, string title = "Confirmation")
        {
            var options = new ThemedMessageBoxOptions
            {
                Message = message,
                Title = title,
                MessageType = MessageBoxType.Question,
                Buttons = MessageBoxButtons.YesNo
            };

            return ShowAsync(options);
        }
    }
}
