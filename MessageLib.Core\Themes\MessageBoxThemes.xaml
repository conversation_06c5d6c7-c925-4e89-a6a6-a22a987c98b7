<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Light Theme -->
    <ResourceDictionary x:Key="LightTheme">
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Light.xaml" />
            <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
        </ResourceDictionary.MergedDictionaries>
        
        <SolidColorBrush x:Key="MessageBoxBackground" Color="#FFFFFF" />
        <SolidColorBrush x:Key="MessageBoxBorder" Color="#E0E0E0" />
        <SolidColorBrush x:Key="MessageBoxHeaderBackground" Color="#F5F5F5" />
        <SolidColorBrush x:Key="MessageBoxText" Color="#212121" />
        <SolidColorBrush x:Key="MessageBoxTitle" Color="#000000" />
    </ResourceDictionary>

    <!-- Dark Theme -->
    <ResourceDictionary x:Key="DarkTheme">
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Dark.xaml" />
            <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
        </ResourceDictionary.MergedDictionaries>
        
        <SolidColorBrush x:Key="MessageBoxBackground" Color="#303030" />
        <SolidColorBrush x:Key="MessageBoxBorder" Color="#424242" />
        <SolidColorBrush x:Key="MessageBoxHeaderBackground" Color="#212121" />
        <SolidColorBrush x:Key="MessageBoxText" Color="#FFFFFF" />
        <SolidColorBrush x:Key="MessageBoxTitle" Color="#FFFFFF" />
    </ResourceDictionary>

    <!-- Button Styles -->
    <Style x:Key="MessageBoxButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="Margin" Value="8,0,0,0" />
        <Setter Property="MinWidth" Value="80" />
        <Setter Property="Height" Value="36" />
    </Style>

    <!-- Information Button Style -->
    <Style x:Key="InformationButtonStyle" TargetType="Button" BasedOn="{StaticResource MessageBoxButtonStyle}">
        <Setter Property="Background" Value="#2196F3" />
        <Setter Property="BorderBrush" Value="#2196F3" />
        <Setter Property="Foreground" Value="White" />
    </Style>

    <!-- Warning Button Style -->
    <Style x:Key="WarningButtonStyle" TargetType="Button" BasedOn="{StaticResource MessageBoxButtonStyle}">
        <Setter Property="Background" Value="#FF9800" />
        <Setter Property="BorderBrush" Value="#FF9800" />
        <Setter Property="Foreground" Value="White" />
    </Style>

    <!-- Error Button Style -->
    <Style x:Key="ErrorButtonStyle" TargetType="Button" BasedOn="{StaticResource MessageBoxButtonStyle}">
        <Setter Property="Background" Value="#F44336" />
        <Setter Property="BorderBrush" Value="#F44336" />
        <Setter Property="Foreground" Value="White" />
    </Style>

    <!-- Success Button Style -->
    <Style x:Key="SuccessButtonStyle" TargetType="Button" BasedOn="{StaticResource MessageBoxButtonStyle}">
        <Setter Property="Background" Value="#4CAF50" />
        <Setter Property="BorderBrush" Value="#4CAF50" />
        <Setter Property="Foreground" Value="White" />
    </Style>

    <!-- Question Button Style -->
    <Style x:Key="QuestionButtonStyle" TargetType="Button" BasedOn="{StaticResource MessageBoxButtonStyle}">
        <Setter Property="Background" Value="#9C27B0" />
        <Setter Property="BorderBrush" Value="#9C27B0" />
        <Setter Property="Foreground" Value="White" />
    </Style>

    <!-- Close Button Style -->
    <Style x:Key="CloseButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="Width" Value="30" />
        <Setter Property="Height" Value="30" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="VerticalAlignment" Value="Top" />
        <Setter Property="HorizontalAlignment" Value="Right" />
        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}" />
    </Style>
</ResourceDictionary>
