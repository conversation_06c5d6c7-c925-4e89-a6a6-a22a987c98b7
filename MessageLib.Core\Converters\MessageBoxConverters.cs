using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;
using MessageLib.Core.Models;
using MaterialDesignThemes.Wpf;

namespace MessageLib.Core.Converters
{
    /// <summary>
    /// Converts a MessageBoxType to a PackIconKind
    /// </summary>
    public class MessageTypeToIconConverter : IValueConverter
    {
        /// <summary>
        /// Converts a MessageBoxType to a PackIconKind
        /// </summary>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is MessageBoxType messageType)
            {
                return messageType switch
                {
                    MessageBoxType.Information => PackIconKind.Information,
                    MessageBoxType.Warning => PackIconKind.Alert,
                    MessageBoxType.Error => PackIconKind.Error,
                    MessageBoxType.Success => PackIconKind.CheckCircle,
                    MessageBoxType.Question => PackIconKind.HelpCircle,
                    _ => PackIconKind.Information
                };
            }

            return PackIconKind.Information;
        }

        /// <summary>
        /// Not implemented
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converts a MessageBoxType to a color
    /// </summary>
    public class MessageTypeToColorConverter : IValueConverter
    {
        /// <summary>
        /// Converts a MessageBoxType to a color
        /// </summary>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is MessageBoxType messageType)
            {
                return messageType switch
                {
                    MessageBoxType.Information => new SolidColorBrush(Colors.DodgerBlue),
                    MessageBoxType.Warning => new SolidColorBrush(Colors.Orange),
                    MessageBoxType.Error => new SolidColorBrush(Colors.Red),
                    MessageBoxType.Success => new SolidColorBrush(Colors.Green),
                    MessageBoxType.Question => new SolidColorBrush(Colors.Purple),
                    _ => new SolidColorBrush(Colors.DodgerBlue)
                };
            }

            return new SolidColorBrush(Colors.DodgerBlue);
        }

        /// <summary>
        /// Not implemented
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    /// <summary>
    /// Converts null to Visibility.Collapsed, non-null to Visibility.Visible
    /// </summary>
    public class NullToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// Converts null to Visibility.Collapsed, non-null to Visibility.Visible
        /// If parameter is "Inverse", the behavior is reversed
        /// </summary>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isNull = value == null;
            bool inverse = parameter != null && parameter.ToString() == "Inverse";

            if (inverse)
                return isNull ? Visibility.Visible : Visibility.Collapsed;
            else
                return isNull ? Visibility.Collapsed : Visibility.Visible;
        }

        /// <summary>
        /// Not implemented
        /// </summary>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
