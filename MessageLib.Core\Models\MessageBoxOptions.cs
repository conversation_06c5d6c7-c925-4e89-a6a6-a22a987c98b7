using System;
using System.Windows;
using System.Windows.Media;

namespace MessageLib.Core.Models
{
    /// <summary>
    /// Represents the type of message box to display
    /// </summary>
    public enum MessageBoxType
    {
        Information,
        Warning,
        Error,
        Success,
        Question,
        None
    }

    /// <summary>
    /// Represents the result of a message box interaction
    /// </summary>
    public enum ThemedMessageBoxResult
    {
        None,
        OK,
        Cancel,
        Yes,
        No,
        Custom
    }

    /// <summary>
    /// Represents the buttons to display in the message box
    /// </summary>
    public enum MessageBoxButtons
    {
        OK,
        OKCancel,
        YesNo,
        YesNoCancel,
        Custom
    }

    /// <summary>
    /// Represents a button in the message box
    /// </summary>
    public class MessageBoxButton
    {
        /// <summary>
        /// Gets or sets the text to display on the button
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// Gets or sets the result value when this button is clicked
        /// </summary>
        public ThemedMessageBoxResult Result { get; set; }

        /// <summary>
        /// Gets or sets the icon to display on the button
        /// </summary>
        public object Icon { get; set; }

        /// <summary>
        /// Gets or sets whether this is the default button
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// Gets or sets whether this is the cancel button
        /// </summary>
        public bool IsCancel { get; set; }
    }

    /// <summary>
    /// Configuration options for the themed message box
    /// </summary>
    public class ThemedMessageBoxOptions
    {
        /// <summary>
        /// Gets or sets the title of the message box
        /// </summary>
        public string Title { get; set; } = "Message";

        /// <summary>
        /// Gets or sets the message content as a string
        /// </summary>
        public string Message { get; set; } = string.Empty;

        /// <summary>
        /// Gets or sets the content as a UIElement (takes precedence over Message if set)
        /// </summary>
        public UIElement ContentElement { get; set; }

        /// <summary>
        /// Gets or sets the type of message box to display
        /// </summary>
        public MessageBoxType MessageType { get; set; } = MessageBoxType.Information;

        /// <summary>
        /// Gets or sets the buttons to display
        /// </summary>
        public MessageBoxButtons Buttons { get; set; } = MessageBoxButtons.OK;

        /// <summary>
        /// Gets or sets custom buttons when Buttons is set to Custom
        /// </summary>
        public MessageBoxButton[] CustomButtons { get; set; }

        /// <summary>
        /// Gets or sets whether to show the close button in the title bar
        /// </summary>
        public bool ShowCloseButton { get; set; } = true;

        /// <summary>
        /// Gets or sets the corner radius for the message box
        /// </summary>
        public double CornerRadius { get; set; } = 8;

        /// <summary>
        /// Gets or sets the width of the message box
        /// </summary>
        public double Width { get; set; } = 400;

        /// <summary>
        /// Gets or sets the minimum height of the message box
        /// </summary>
        public double MinHeight { get; set; } = 200;

        /// <summary>
        /// Gets or sets the maximum height of the message box
        /// </summary>
        public double MaxHeight { get; set; } = 600;

        /// <summary>
        /// Gets or sets whether to use light theme
        /// </summary>
        public bool UseLightTheme { get; set; } = true;

        /// <summary>
        /// Gets or sets the primary color for the message box
        /// </summary>
        public Color? PrimaryColor { get; set; }

        /// <summary>
        /// Gets or sets the accent color for the message box
        /// </summary>
        public Color? AccentColor { get; set; }

        /// <summary>
        /// Gets or sets the owner window for the message box
        /// </summary>
        public Window Owner { get; set; }

        /// <summary>
        /// Gets or sets whether to show the message box as a dialog
        /// </summary>
        public bool ShowAsDialog { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to use animations
        /// </summary>
        public bool UseAnimations { get; set; } = true;

        /// <summary>
        /// Gets or sets whether to show the title bar
        /// </summary>
        public bool ShowTitleBar { get; set; } = true;
    }
}
