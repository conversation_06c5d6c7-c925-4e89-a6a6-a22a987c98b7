using System;
using System.Threading.Tasks;
using System.Windows;
using MessageLib.Core.Models;

namespace MessageLib.Core.Services
{
    /// <summary>
    /// Interface for the message box service
    /// </summary>
    public interface IMessageBoxService
    {
        /// <summary>
        /// Shows a message box with the specified options
        /// </summary>
        /// <param name="options">The options for the message box</param>
        /// <returns>The result of the message box interaction</returns>
        ThemedMessageBoxResult Show(ThemedMessageBoxOptions options);

        /// <summary>
        /// Shows a message box with the specified options asynchronously
        /// </summary>
        /// <param name="options">The options for the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        Task<ThemedMessageBoxResult> ShowAsync(ThemedMessageBoxOptions options);

        /// <summary>
        /// Shows a simple message box with the specified message
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        ThemedMessageBoxResult Show(string message, string title = "Message");

        /// <summary>
        /// Shows a simple message box with the specified message asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        Task<ThemedMessageBoxResult> ShowAsync(string message, string title = "Message");

        /// <summary>
        /// Shows an information message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        ThemedMessageBoxResult ShowInformation(string message, string title = "Information");

        /// <summary>
        /// Shows an information message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        Task<ThemedMessageBoxResult> ShowInformationAsync(string message, string title = "Information");

        /// <summary>
        /// Shows a warning message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        ThemedMessageBoxResult ShowWarning(string message, string title = "Warning");

        /// <summary>
        /// Shows a warning message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        Task<ThemedMessageBoxResult> ShowWarningAsync(string message, string title = "Warning");

        /// <summary>
        /// Shows an error message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        ThemedMessageBoxResult ShowError(string message, string title = "Error");

        /// <summary>
        /// Shows an error message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        Task<ThemedMessageBoxResult> ShowErrorAsync(string message, string title = "Error");

        /// <summary>
        /// Shows a success message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        ThemedMessageBoxResult ShowSuccess(string message, string title = "Success");

        /// <summary>
        /// Shows a success message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        Task<ThemedMessageBoxResult> ShowSuccessAsync(string message, string title = "Success");

        /// <summary>
        /// Shows a question message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        ThemedMessageBoxResult ShowQuestion(string message, string title = "Question");

        /// <summary>
        /// Shows a question message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        Task<ThemedMessageBoxResult> ShowQuestionAsync(string message, string title = "Question");

        /// <summary>
        /// Shows a confirmation message box with Yes/No buttons
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        ThemedMessageBoxResult ShowConfirmation(string message, string title = "Confirmation");

        /// <summary>
        /// Shows a confirmation message box with Yes/No buttons asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        Task<ThemedMessageBoxResult> ShowConfirmationAsync(string message, string title = "Confirmation");
    }
}
