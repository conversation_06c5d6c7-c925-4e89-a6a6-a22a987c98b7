
using MessageLib.Core.Models;
using MessageLib.Core.Services;
using System.Threading.Tasks;
using System.Windows;

// Use alias to avoid conflicts with System.Windows.MessageBoxResult
using WpfMessageBoxResult = System.Windows.MessageBoxResult;
using WpfMessageBoxOptions = System.Windows.MessageBoxOptions;

namespace MessageLib.Core
{
    /// <summary>
    /// Main entry point for the MessageLib library
    /// </summary>
    public static class MessageBox
    {
        private static readonly IMessageBoxService _messageBoxService = new MessageBoxService();

        /// <summary>
        /// Shows a message box with the specified options
        /// </summary>
        /// <param name="options">The options for the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public static ThemedMessageBoxResult Show(ThemedMessageBoxOptions options)
        {
            return _messageBoxService.Show(options);
        }

        /// <summary>
        /// Shows a message box with the specified options asynchronously
        /// </summary>
        /// <param name="options">The options for the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public static Task<ThemedMessageBoxResult> ShowAsync(ThemedMessageBoxOptions options)
        {
            return _messageBoxService.ShowAsync(options);
        }

        /// <summary>
        /// Shows a simple message box with the specified message
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public static ThemedMessageBoxResult Show(string message, string title = "Message")
        {
            return _messageBoxService.Show(message, title);
        }

        /// <summary>
        /// Shows a simple message box with the specified message asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public static Task<ThemedMessageBoxResult> ShowAsync(string message, string title = "Message")
        {
            return _messageBoxService.ShowAsync(message, title);
        }

        /// <summary>
        /// Shows an information message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public static ThemedMessageBoxResult ShowInformation(string message, string title = "Information")
        {
            return _messageBoxService.ShowInformation(message, title);
        }

        /// <summary>
        /// Shows an information message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public static Task<ThemedMessageBoxResult> ShowInformationAsync(string message, string title = "Information")
        {
            return _messageBoxService.ShowInformationAsync(message, title);
        }

        /// <summary>
        /// Shows a warning message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public static ThemedMessageBoxResult ShowWarning(string message, string title = "Warning")
        {
            return _messageBoxService.ShowWarning(message, title);
        }

        /// <summary>
        /// Shows a warning message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public static Task<ThemedMessageBoxResult> ShowWarningAsync(string message, string title = "Warning")
        {
            return _messageBoxService.ShowWarningAsync(message, title);
        }

        /// <summary>
        /// Shows an error message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public static ThemedMessageBoxResult ShowError(string message, string title = "Error")
        {
            return _messageBoxService.ShowError(message, title);
        }

        /// <summary>
        /// Shows an error message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public static Task<ThemedMessageBoxResult> ShowErrorAsync(string message, string title = "Error")
        {
            return _messageBoxService.ShowErrorAsync(message, title);
        }

        /// <summary>
        /// Shows a success message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public static ThemedMessageBoxResult ShowSuccess(string message, string title = "Success")
        {
            return _messageBoxService.ShowSuccess(message, title);
        }

        /// <summary>
        /// Shows a success message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public static Task<ThemedMessageBoxResult> ShowSuccessAsync(string message, string title = "Success")
        {
            return _messageBoxService.ShowSuccessAsync(message, title);
        }

        /// <summary>
        /// Shows a question message box
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public static ThemedMessageBoxResult ShowQuestion(string message, string title = "Question")
        {
            return _messageBoxService.ShowQuestion(message, title);
        }

        /// <summary>
        /// Shows a question message box asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public static Task<ThemedMessageBoxResult> ShowQuestionAsync(string message, string title = "Question")
        {
            return _messageBoxService.ShowQuestionAsync(message, title);
        }

        /// <summary>
        /// Shows a confirmation message box with Yes/No buttons
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>The result of the message box interaction</returns>
        public static ThemedMessageBoxResult ShowConfirmation(string message, string title = "Confirmation")
        {
            return _messageBoxService.ShowConfirmation(message, title);
        }

        /// <summary>
        /// Shows a confirmation message box with Yes/No buttons asynchronously
        /// </summary>
        /// <param name="message">The message to display</param>
        /// <param name="title">The title of the message box</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public static Task<ThemedMessageBoxResult> ShowConfirmationAsync(string message, string title = "Confirmation")
        {
            return _messageBoxService.ShowConfirmationAsync(message, title);
        }
    }
}
