using System;
using System.Windows;
using System.Windows.Media;

namespace MessageLib.Core.Themes
{
    /// <summary>
    /// Manages themes for the message box
    /// </summary>
    public static class ThemeManager
    {
        private static ResourceDictionary _lightTheme;
        private static ResourceDictionary _darkTheme;
        private static bool _isInitialized;

        /// <summary>
        /// Initializes the theme manager
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized)
                return;

            _lightTheme = new ResourceDictionary
            {
                Source = new Uri("pack://application:,,,/MessageLib.Core;component/Themes/MessageBoxThemes.xaml")
            };

            _darkTheme = new ResourceDictionary
            {
                Source = new Uri("pack://application:,,,/MessageLib.Core;component/Themes/MessageBoxThemes.xaml")
            };

            _isInitialized = true;
        }

        /// <summary>
        /// Applies the specified theme to the message box
        /// </summary>
        /// <param name="window">The window to apply the theme to</param>
        /// <param name="useLightTheme">Whether to use the light theme</param>
        /// <param name="primaryColor">The primary color for the theme</param>
        /// <param name="accentColor">The accent color for the theme</param>
        public static void ApplyTheme(Window window, bool useLightTheme, Color? primaryColor = null, Color? accentColor = null)
        {
            if (!_isInitialized)
                Initialize();

            var theme = useLightTheme ? _lightTheme["LightTheme"] as ResourceDictionary : _darkTheme["DarkTheme"] as ResourceDictionary;

            if (theme != null)
            {
                // Apply the theme to the window
                window.Resources.MergedDictionaries.Clear();
                window.Resources.MergedDictionaries.Add(theme);

                // Apply custom colors if specified
                if (primaryColor.HasValue)
                {
                    var primaryBrush = new SolidColorBrush(primaryColor.Value);
                    window.Resources["PrimaryHueMidBrush"] = primaryBrush;
                }

                if (accentColor.HasValue)
                {
                    var accentBrush = new SolidColorBrush(accentColor.Value);
                    window.Resources["SecondaryAccentBrush"] = accentBrush;
                }
            }
        }
    }
}
