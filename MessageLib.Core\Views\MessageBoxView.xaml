<Window x:Class="MessageLib.Core.Views.MessageBoxView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:viewModels="clr-namespace:MessageLib.Core.ViewModels"
        xmlns:models="clr-namespace:MessageLib.Core.Models"
        xmlns:converters="clr-namespace:MessageLib.Core.Converters"
        mc:Ignorable="d"
        Title="Message"
        Width="400"
        Height="250"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        d:DataContext="{d:DesignInstance Type=viewModels:MessageBoxViewModel}">

    <Window.Resources>
        <ResourceDictionary>
            <!-- Light theme resources -->
            <ResourceDictionary x:Key="LightTheme">
                <SolidColorBrush x:Key="WindowBackground" Color="#FFFFFF" />
                <SolidColorBrush x:Key="TextForeground" Color="#212121" />
                <SolidColorBrush x:Key="ButtonBackground" Color="#2196F3" />
                <SolidColorBrush x:Key="ButtonForeground" Color="#FFFFFF" />
                <SolidColorBrush x:Key="BorderBrush" Color="#BDBDBD" />
            </ResourceDictionary>

            <!-- Dark theme resources -->
            <ResourceDictionary x:Key="DarkTheme">
                <SolidColorBrush x:Key="WindowBackground" Color="#303030" />
                <SolidColorBrush x:Key="TextForeground" Color="#FFFFFF" />
                <SolidColorBrush x:Key="ButtonBackground" Color="#1976D2" />
                <SolidColorBrush x:Key="ButtonForeground" Color="#FFFFFF" />
                <SolidColorBrush x:Key="BorderBrush" Color="#616161" />
            </ResourceDictionary>

            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
            <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
            <converters:BooleanToResourceConverter x:Key="ThemeConverter"
                                                  TrueValue="{StaticResource LightTheme}"
                                                  FalseValue="{StaticResource DarkTheme}" />

            <!-- Drop shadow effect for the window -->
            <DropShadowEffect x:Key="DropShadowEffect"
                             ShadowDepth="5"
                             Direction="315"
                             Color="#22000000"
                             BlurRadius="10"
                             Opacity="0.5" />
        </ResourceDictionary>
    </Window.Resources>

    <!-- Window style with animations -->
    <Window.Style>
        <Style TargetType="Window">
            <!-- Always start with opacity 1 -->
            <Setter Property="Opacity" Value="1" />
        </Style>
    </Window.Style>



    <!-- Apply theme based on UseLightTheme property -->
    <Window.Background>
        <SolidColorBrush Color="Transparent" />
    </Window.Background>

    <Border BorderBrush="{Binding UseLightTheme, Converter={StaticResource ThemeConverter}, ConverterParameter='BorderBrush'}"
            BorderThickness="{Binding EffectiveBorderThickness}"
            CornerRadius="{Binding EffectiveCornerRadius}"
            Background="{Binding UseLightTheme, Converter={StaticResource ThemeConverter}, ConverterParameter='WindowBackground'}"
            Effect="{StaticResource DropShadowEffect}">
        <Grid Margin="20">
            <Grid.RenderTransform>
                <ScaleTransform ScaleX="1" ScaleY="1" />
            </Grid.RenderTransform>

            <Grid.Style>
                <Style TargetType="Grid">
                    <!-- Default opacity -->
                    <Setter Property="Opacity" Value="1" />
                    <Style.Triggers>
                        <!-- Set initial opacity to 0 when animations are enabled -->
                        <DataTrigger Binding="{Binding UseAnimations}" Value="True">
                            <Setter Property="Opacity" Value="0" />
                        </DataTrigger>

                        <!-- Animation for showing with fade in (only when animations enabled) -->
                        <MultiDataTrigger>
                            <MultiDataTrigger.Conditions>
                                <Condition Binding="{Binding UseAnimations}" Value="True" />
                                <Condition Binding="{Binding IsShowing}" Value="True" />
                            </MultiDataTrigger.Conditions>
                            <MultiDataTrigger.EnterActions>
                                <BeginStoryboard Name="ShowAnimation">
                                    <Storyboard>
                                        <!-- Fade in animation -->
                                        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                                       From="0" To="1"
                                                       Duration="0:0:0.4">
                                            <DoubleAnimation.EasingFunction>
                                                <CubicEase EasingMode="EaseOut" />
                                            </DoubleAnimation.EasingFunction>
                                        </DoubleAnimation>
                                        <!-- Scale animation -->
                                        <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                       From="0.9" To="1"
                                                       Duration="0:0:0.4">
                                            <DoubleAnimation.EasingFunction>
                                                <BackEase EasingMode="EaseOut" Amplitude="0.3" />
                                            </DoubleAnimation.EasingFunction>
                                        </DoubleAnimation>
                                        <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                       From="0.9" To="1"
                                                       Duration="0:0:0.4">
                                            <DoubleAnimation.EasingFunction>
                                                <BackEase EasingMode="EaseOut" Amplitude="0.3" />
                                            </DoubleAnimation.EasingFunction>
                                        </DoubleAnimation>
                                    </Storyboard>
                                </BeginStoryboard>
                            </MultiDataTrigger.EnterActions>
                        </MultiDataTrigger>

                        <!-- Animation for closing with fade out -->
                        <DataTrigger Binding="{Binding IsClosing}" Value="True">
                            <DataTrigger.EnterActions>
                                <BeginStoryboard Name="CloseAnimation">
                                    <Storyboard>
                                        <!-- Fade out animation -->
                                        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                                       From="1" To="0"
                                                       Duration="0:0:0.25">
                                            <DoubleAnimation.EasingFunction>
                                                <CubicEase EasingMode="EaseIn" />
                                            </DoubleAnimation.EasingFunction>
                                        </DoubleAnimation>
                                        <!-- Scale animation -->
                                        <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleX"
                                                       From="1" To="0.9"
                                                       Duration="0:0:0.25">
                                            <DoubleAnimation.EasingFunction>
                                                <CubicEase EasingMode="EaseIn" />
                                            </DoubleAnimation.EasingFunction>
                                        </DoubleAnimation>
                                        <DoubleAnimation Storyboard.TargetProperty="RenderTransform.ScaleY"
                                                       From="1" To="0.9"
                                                       Duration="0:0:0.25">
                                            <DoubleAnimation.EasingFunction>
                                                <CubicEase EasingMode="EaseIn" />
                                            </DoubleAnimation.EasingFunction>
                                        </DoubleAnimation>
                                    </Storyboard>
                                </BeginStoryboard>
                            </DataTrigger.EnterActions>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Grid.Style>

            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="*" />
                <RowDefinition Height="Auto" />
            </Grid.RowDefinitions>

            <!-- Header -->
            <TextBlock Grid.Row="0"
                       Text="{Binding Title}"
                       FontSize="16"
                       FontWeight="Bold"
                       Margin="0,0,0,10"
                       Foreground="{Binding UseLightTheme, Converter={StaticResource ThemeConverter}, ConverterParameter='TextForeground'}" />

        <!-- Content -->
        <TextBlock Grid.Row="1"
                   Text="{Binding Message}"
                   TextWrapping="Wrap"
                   VerticalAlignment="Center"
                   Foreground="{Binding UseLightTheme, Converter={StaticResource ThemeConverter}, ConverterParameter='TextForeground'}" />

        <!-- Buttons -->
        <StackPanel Grid.Row="2"
                    Orientation="Horizontal"
                    HorizontalAlignment="Right"
                    Margin="0,10,0,0">
            <ItemsControl ItemsSource="{Binding Buttons}">
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <StackPanel Orientation="Horizontal" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Button Content="{Binding Text}"
                                Command="{Binding ClickCommand}"
                                IsDefault="{Binding IsDefault}"
                                IsCancel="{Binding IsCancel}"
                                Margin="5,0,0,0"
                                Padding="10,5"
                                MinWidth="80">
                            <!-- Use custom colors if provided, otherwise use theme colors -->
                            <Button.Style>
                                <Style TargetType="Button" BasedOn="{StaticResource {x:Type Button}}">
                                    <Setter Property="Background" Value="{Binding DataContext.UseLightTheme, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource ThemeConverter}, ConverterParameter='ButtonBackground'}" />
                                    <Setter Property="Foreground" Value="{Binding DataContext.UseLightTheme, RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource ThemeConverter}, ConverterParameter='ButtonForeground'}" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding DataContext.HasCustomColors, RelativeSource={RelativeSource AncestorType=Window}}" Value="True">
                                            <Setter Property="Background" Value="{Binding DataContext.PrimaryColorBrush, RelativeSource={RelativeSource AncestorType=Window}}" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </StackPanel>
    </Grid>
    </Border>
</Window>
