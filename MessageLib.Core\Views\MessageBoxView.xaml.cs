using System;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using MessageLib.Core.ViewModels;
using MessageLib.Core.Models;

namespace MessageLib.Core.Views
{
    /// <summary>
    /// Interaction logic for MessageBoxView.xaml
    /// </summary>
    public partial class MessageBoxView : Window
    {
        /// <summary>
        /// Initializes a new instance of the <see cref="MessageBoxView"/> class
        /// </summary>
        public MessageBoxView()
        {
            InitializeComponent();

            // Set window properties to ensure it's visible and properly positioned
            WindowStartupLocation = WindowStartupLocation.CenterScreen;
            ShowInTaskbar = true;
            Topmost = true;

            // Handle the loaded event to ensure the window is properly focused
            Loaded += OnWindowLoaded;

            // Handle the content rendered event
            ContentRendered += OnContentRendered;

            // Handle the key down event for Escape key
            KeyDown += OnKeyDown;

            // Handle mouse down event for dragging the window
            MouseDown += OnMouseDown;
        }



        private void OnMouseDown(object sender, MouseButtonEventArgs e)
        {
            // Allow dragging the window when the left mouse button is pressed
            if (e.ChangedButton == MouseButton.Left)
            {
                DragMove();
            }
        }

        private void OnWindowLoaded(object sender, RoutedEventArgs e)
        {
            // Ensure the window is visible
            Visibility = Visibility.Visible;

            // Activate and focus the window
            Activate();
            Focus();

            // Trigger the show animation if the DataContext is available
            if (DataContext is MessageBoxViewModel viewModel)
            {
                // Set window clipping for rounded corners when no title bar
                if (!viewModel.ShowTitleBar && viewModel.CornerRadius > 0)
                {
                    SetWindowClip(viewModel.CornerRadius);
                }

                // Use dispatcher to ensure the animation triggers after the window is fully loaded
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    // Always trigger the show animation (it will only animate if UseAnimations is true)
                    viewModel.IsShowing = true;
                }), System.Windows.Threading.DispatcherPriority.Loaded);
            }
        }

        private void OnContentRendered(object sender, EventArgs e)
        {
            // Bring the window to the front after content is rendered
            Topmost = true;
            Topmost = false;

            // Activate and focus again after content is rendered
            Activate();
            Focus();
        }

        private void OnKeyDown(object sender, KeyEventArgs e)
        {
            // Close the window when Escape key is pressed
            if (e.Key == Key.Escape)
            {
                if (DataContext is MessageBoxViewModel viewModel)
                {
                    viewModel.Close(ThemedMessageBoxResult.Cancel);
                }
                Close();
            }
        }

        /// <summary>
        /// Sets the window clip region to create rounded corners
        /// </summary>
        /// <param name="cornerRadius">The corner radius to apply</param>
        private void SetWindowClip(double cornerRadius)
        {
            try
            {
                // Set the clip geometry for the window
                Clip = new RectangleGeometry(new Rect(0, 0, Width, Height), cornerRadius, cornerRadius);

                // Also handle size changes
                SizeChanged += (s, e) =>
                {
                    if (DataContext is MessageBoxViewModel vm && !vm.ShowTitleBar && vm.CornerRadius > 0)
                    {
                        Clip = new RectangleGeometry(new Rect(0, 0, e.NewSize.Width, e.NewSize.Height), cornerRadius, cornerRadius);
                    }
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error setting window clip: {ex.Message}");
            }
        }

        /// <summary>
        /// Called when the window is closing
        /// </summary>
        protected override void OnClosing(System.ComponentModel.CancelEventArgs e)
        {
            if (DataContext is MessageBoxViewModel viewModel && viewModel.Result == ThemedMessageBoxResult.None)
            {
                // If no result has been set, set a default result
                viewModel.Result = ThemedMessageBoxResult.Cancel;
            }

            base.OnClosing(e);
        }

        /// <summary>
        /// Called when the window is closed
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            // Clean up resources
            DataContext = null;

            base.OnClosed(e);
        }
    }
}
