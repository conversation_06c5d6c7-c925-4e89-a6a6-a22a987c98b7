﻿<Window x:Class="MessageLib.Demo.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:MessageLib.Demo"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        mc:Ignorable="d"
        Title="MessageLib Demo" Height="600" Width="800"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Ideal"
        TextOptions.TextRenderingMode="Auto"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Window.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Light.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Primary/MaterialDesignColor.DeepPurple.xaml" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignColors;component/Themes/Recommended/Accent/MaterialDesignColor.Lime.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </Window.Resources>

    <Grid Margin="16">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <TextBlock Grid.Row="0"
                   Text="MessageLib Demo"
                   Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                   Margin="0,0,0,16" />

        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <GroupBox Header="Basic Message Boxes" Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <Button Content="Show Simple Message"
                                Margin="0,8,0,0"
                                Click="SimpleMessage_Click" />

                        <Button Content="Show Information Message"
                                Margin="0,8,0,0"
                                Click="InformationMessage_Click" />

                        <Button Content="Show Warning Message"
                                Margin="0,8,0,0"
                                Click="WarningMessage_Click" />

                        <Button Content="Show Error Message"
                                Margin="0,8,0,0"
                                Click="ErrorMessage_Click" />

                        <Button Content="Show Success Message"
                                Margin="0,8,0,0"
                                Click="SuccessMessage_Click" />

                        <Button Content="Show Question Message"
                                Margin="0,8,0,0"
                                Click="QuestionMessage_Click" />

                        <Button Content="Show Confirmation Message"
                                Margin="0,8,0,0"
                                Click="ConfirmationMessage_Click" />
                    </StackPanel>
                </GroupBox>

                <GroupBox Header="Advanced Options" Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <Button Content="Custom Buttons"
                                Margin="0,8,0,0"
                                Click="CustomButtons_Click" />

                        <Button Content="Rich Content"
                                Margin="0,8,0,0"
                                Click="RichContent_Click" />

                        <Button Content="Async Message Box"
                                Margin="0,8,0,0"
                                Click="AsyncMessage_Click" />
                    </StackPanel>
                </GroupBox>

                <GroupBox Header="Theming" Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <Button Content="Light Theme"
                                Margin="0,8,0,0"
                                Click="LightTheme_Click" />

                        <Button Content="Dark Theme"
                                Margin="0,8,0,0"
                                Click="DarkTheme_Click" />

                        <Button Content="Custom Colors"
                                Margin="0,8,0,0"
                                Click="CustomColors_Click" />
                    </StackPanel>
                </GroupBox>

                <GroupBox Header="Animation" Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <Button Content="With Animation"
                                Margin="0,8,0,0"
                                Click="WithAnimation_Click" />

                        <Button Content="Without Animation"
                                Margin="0,8,0,0"
                                Click="WithoutAnimation_Click" />
                    </StackPanel>
                </GroupBox>

                <GroupBox Header="Title Bar" Margin="0,0,0,16">
                    <StackPanel Margin="8">
                        <Button Content="With Title Bar"
                                Margin="0,8,0,0"
                                Click="WithTitleBar_Click" />

                        <Button Content="Without Title Bar"
                                Margin="0,8,0,0"
                                Click="WithoutTitleBar_Click" />
                    </StackPanel>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</Window>
