﻿<Application x:Class="MessageLib.Demo.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:local="clr-namespace:MessageLib.Demo"
             xmlns:converters="clr-namespace:MessageLib.Core.Converters;assembly=MessageLib.Core"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             StartupUri="MainWindow.xaml">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <converters:MessageTypeToIconConverter x:Key="MessageTypeToIconConverter" />
            <converters:MessageTypeToColorConverter x:Key="MessageTypeToColorConverter" />
            <converters:NullToVisibilityConverter x:Key="NullToVisibilityConverter" />
            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter" />
        </ResourceDictionary>
    </Application.Resources>
</Application>
