using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using MessageLib.Core.Models;

// Use alias to avoid conflicts with System.Windows.MessageBoxResult
using WpfMessageBoxResult = System.Windows.MessageBoxResult;
using WpfMessageBoxOptions = System.Windows.MessageBoxOptions;

namespace MessageLib.Core.ViewModels
{
    /// <summary>
    /// ViewModel for the MessageBox control
    /// </summary>
    public partial class MessageBoxViewModel : ObservableObject
    {
        private TaskCompletionSource<ThemedMessageBoxResult> _resultSource;
        private ThemedMessageBoxOptions _options;

        /// <summary>
        /// Gets or sets the title of the message box
        /// </summary>
        [ObservableProperty]
        private string _title;

        /// <summary>
        /// Gets or sets the message content
        /// </summary>
        [ObservableProperty]
        private string _message;

        /// <summary>
        /// Gets or sets the content element
        /// </summary>
        [ObservableProperty]
        private UIElement _contentElement;

        /// <summary>
        /// Gets or sets the message type
        /// </summary>
        [ObservableProperty]
        private MessageBoxType _messageType;

        /// <summary>
        /// Gets or sets the buttons to display
        /// </summary>
        [ObservableProperty]
        private ObservableCollection<MessageBoxButtonViewModel> _buttons;

        /// <summary>
        /// Gets or sets whether to show the close button
        /// </summary>
        [ObservableProperty]
        private bool _showCloseButton;

        /// <summary>
        /// Gets or sets the corner radius
        /// </summary>
        [ObservableProperty]
        private double _cornerRadius;

        /// <summary>
        /// Gets the effective corner radius based on window style
        /// When ShowTitleBar is false (borderless), we can use real rounded corners with AllowsTransparency
        /// When ShowTitleBar is true (with title bar), rounded corners create visual artifacts, so use 0
        /// </summary>
        public double EffectiveCornerRadius => ShowTitleBar ? 0 : CornerRadius;

        /// <summary>
        /// Gets the effective border thickness based on window style
        /// </summary>
        public System.Windows.Thickness EffectiveBorderThickness => ShowTitleBar ? new System.Windows.Thickness(1) : new System.Windows.Thickness(1);

        /// <summary>
        /// Called when ShowTitleBar property changes
        /// </summary>
        partial void OnShowTitleBarChanged(bool value)
        {
            // Notify that EffectiveCornerRadius and EffectiveBorderThickness have changed
            OnPropertyChanged(nameof(EffectiveCornerRadius));
            OnPropertyChanged(nameof(EffectiveBorderThickness));
        }

        /// <summary>
        /// Called when CornerRadius property changes
        /// </summary>
        partial void OnCornerRadiusChanged(double value)
        {
            // Notify that EffectiveCornerRadius has changed
            OnPropertyChanged(nameof(EffectiveCornerRadius));
        }

        /// <summary>
        /// Gets or sets whether to use light theme
        /// </summary>
        [ObservableProperty]
        private bool _useLightTheme = true; // Default to light theme

        /// <summary>
        /// Gets or sets whether to use animations
        /// </summary>
        [ObservableProperty]
        private bool _useAnimations;

        /// <summary>
        /// Gets or sets whether to show the title bar
        /// </summary>
        [ObservableProperty]
        private bool _showTitleBar = true;

        /// <summary>
        /// Gets or sets the width of the message box
        /// </summary>
        [ObservableProperty]
        private double _width;

        /// <summary>
        /// Gets or sets the minimum height of the message box
        /// </summary>
        [ObservableProperty]
        private double _minHeight;

        /// <summary>
        /// Gets or sets the maximum height of the message box
        /// </summary>
        [ObservableProperty]
        private double _maxHeight;

        /// <summary>
        /// Gets or sets whether the message box is visible
        /// </summary>
        [ObservableProperty]
        private bool _isVisible;

        /// <summary>
        /// Gets or sets whether the message box is showing (for animation trigger)
        /// </summary>
        [ObservableProperty]
        private bool _isShowing;

        /// <summary>
        /// Gets or sets whether the message box is closing
        /// </summary>
        [ObservableProperty]
        private bool _isClosing;

        /// <summary>
        /// Gets or sets whether the message box has custom colors
        /// </summary>
        [ObservableProperty]
        private bool _hasCustomColors;

        /// <summary>
        /// Gets or sets the primary color brush
        /// </summary>
        [ObservableProperty]
        private System.Windows.Media.SolidColorBrush _primaryColorBrush;



        /// <summary>
        /// Gets whether the result has been set
        /// </summary>
        public bool IsResultSet => _resultSource?.Task.IsCompleted ?? false;

        /// <summary>
        /// Gets or sets the result of the message box interaction
        /// </summary>
        public ThemedMessageBoxResult Result { get; set; } = ThemedMessageBoxResult.None;

        /// <summary>
        /// Initializes a new instance of the <see cref="MessageBoxViewModel"/> class
        /// </summary>
        public MessageBoxViewModel()
        {
            Buttons = new ObservableCollection<MessageBoxButtonViewModel>();
        }

        /// <summary>
        /// Initializes the message box with the specified options
        /// </summary>
        /// <param name="options">The options for the message box</param>
        public void Initialize(ThemedMessageBoxOptions options)
        {
            _options = options;

            // Create a new TaskCompletionSource for each message box
            _resultSource = new TaskCompletionSource<ThemedMessageBoxResult>(TaskCreationOptions.RunContinuationsAsynchronously);

            Title = options.Title;
            Message = options.Message;
            ContentElement = options.ContentElement;
            MessageType = options.MessageType;
            ShowCloseButton = options.ShowCloseButton;
            CornerRadius = options.CornerRadius;
            UseLightTheme = options.UseLightTheme;
            UseAnimations = options.UseAnimations;
            ShowTitleBar = options.ShowTitleBar;
            Width = options.Width;
            MinHeight = options.MinHeight;
            MaxHeight = options.MaxHeight;

            // Set custom colors if provided
            HasCustomColors = options.PrimaryColor.HasValue;
            if (options.PrimaryColor.HasValue)
            {
                PrimaryColorBrush = new System.Windows.Media.SolidColorBrush(options.PrimaryColor.Value);
            }

            InitializeButtons(options);
        }

        /// <summary>
        /// Initializes the buttons based on the options
        /// </summary>
        /// <param name="options">The options for the message box</param>
        private void InitializeButtons(ThemedMessageBoxOptions options)
        {
            Buttons.Clear();

            if (options.Buttons == MessageBoxButtons.Custom && options.CustomButtons != null)
            {
                foreach (var button in options.CustomButtons)
                {
                    var buttonViewModel = new MessageBoxButtonViewModel
                    {
                        Text = button.Text,
                        Result = button.Result,
                        Icon = button.Icon,
                        IsDefault = button.IsDefault,
                        IsCancel = button.IsCancel
                    };
                    buttonViewModel.ClickCommand = new RelayCommand(() => HandleButtonClick(buttonViewModel.Result));
                    Buttons.Add(buttonViewModel);
                }
            }
            else
            {
                switch (options.Buttons)
                {
                    case MessageBoxButtons.OK:
                        AddButton("OK", ThemedMessageBoxResult.OK, isDefault: true, isCancel: true);
                        break;
                    case MessageBoxButtons.OKCancel:
                        AddButton("OK", ThemedMessageBoxResult.OK, isDefault: true);
                        AddButton("Cancel", ThemedMessageBoxResult.Cancel, isCancel: true);
                        break;
                    case MessageBoxButtons.YesNo:
                        AddButton("Yes", ThemedMessageBoxResult.Yes, isDefault: true);
                        AddButton("No", ThemedMessageBoxResult.No, isCancel: true);
                        break;
                    case MessageBoxButtons.YesNoCancel:
                        AddButton("Yes", ThemedMessageBoxResult.Yes, isDefault: true);
                        AddButton("No", ThemedMessageBoxResult.No);
                        AddButton("Cancel", ThemedMessageBoxResult.Cancel, isCancel: true);
                        break;
                }
            }
        }

        /// <summary>
        /// Adds a button to the buttons collection
        /// </summary>
        private void AddButton(string text, ThemedMessageBoxResult result, bool isDefault = false, bool isCancel = false)
        {
            var buttonViewModel = new MessageBoxButtonViewModel
            {
                Text = text,
                Result = result,
                IsDefault = isDefault,
                IsCancel = isCancel
            };
            buttonViewModel.ClickCommand = new RelayCommand(() => HandleButtonClick(buttonViewModel.Result));
            Buttons.Add(buttonViewModel);
        }

        /// <summary>
        /// Handles a button click
        /// </summary>
        /// <param name="result">The result of the button click</param>
        private void HandleButtonClick(ThemedMessageBoxResult result)
        {
            // Set the result
            Result = result;

            // If animations are enabled, set the IsClosing flag and delay closing the window
            if (UseAnimations)
            {
                IsClosing = true;

                // Delay closing the window to allow the fade out animation to complete (250ms)
                Task.Delay(300).ContinueWith(_ =>
                {
                    Application.Current?.Dispatcher?.Invoke(() =>
                    {
                        CloseParentWindow();
                    });
                });
            }
            else
            {
                // Close the window immediately
                CloseParentWindow();
            }
        }

        /// <summary>
        /// Closes the parent window
        /// </summary>
        private void CloseParentWindow()
        {
            try
            {
                if (Application.Current != null)
                {
                    foreach (Window window in Application.Current.Windows)
                    {
                        if (window.DataContext == this)
                        {
                            window.Close();
                            break;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error closing window: {ex.Message}");
            }
        }

        /// <summary>
        /// Command to close the message box
        /// </summary>
        [RelayCommand]
        private void Close()
        {
            // Close with cancel result
            HandleButtonClick(ThemedMessageBoxResult.Cancel);
        }

        /// <summary>
        /// Closes the message box with the specified result
        /// </summary>
        /// <param name="result">The result of the message box interaction</param>
        public void Close(ThemedMessageBoxResult result)
        {
            // Set the result
            Result = result;

            // If animations are enabled, set the IsClosing flag and delay closing the window
            if (UseAnimations)
            {
                IsClosing = true;

                // Delay closing the window to allow the fade out animation to complete (250ms)
                Task.Delay(300).ContinueWith(_ =>
                {
                    Application.Current?.Dispatcher?.Invoke(() =>
                    {
                        CloseParentWindow();
                    });
                });
            }
            else
            {
                // Close the window immediately
                CloseParentWindow();
            }
        }

        /// <summary>
        /// Shows the message box
        /// </summary>
        /// <returns>A task that represents the asynchronous operation. The task result contains the result of the message box interaction</returns>
        public Task<ThemedMessageBoxResult> ShowAsync()
        {
            // Make sure the window is visible
            IsVisible = true;

            // Create a new TaskCompletionSource for each call
            _resultSource = new TaskCompletionSource<ThemedMessageBoxResult>(TaskCreationOptions.RunContinuationsAsynchronously);

            // Return the task that will complete when the result is set
            return _resultSource.Task;
        }
    }

    /// <summary>
    /// ViewModel for a message box button
    /// </summary>
    public class MessageBoxButtonViewModel : ObservableObject
    {
        /// <summary>
        /// Gets or sets the text to display on the button
        /// </summary>
        public string Text { get; set; }

        /// <summary>
        /// Gets or sets the result value when this button is clicked
        /// </summary>
        public ThemedMessageBoxResult Result { get; set; }

        /// <summary>
        /// Gets or sets the icon to display on the button
        /// </summary>
        public object Icon { get; set; }

        /// <summary>
        /// Gets or sets whether this is the default button
        /// </summary>
        public bool IsDefault { get; set; }

        /// <summary>
        /// Gets or sets whether this is the cancel button
        /// </summary>
        public bool IsCancel { get; set; }

        /// <summary>
        /// Gets or sets the command to execute when the button is clicked
        /// </summary>
        public ICommand ClickCommand { get; set; }
    }
}
